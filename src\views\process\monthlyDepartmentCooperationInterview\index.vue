<!--月度部门合作访谈-->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="年份" prop="yearValue">
        <el-select v-model="queryParams.yearValue" placeholder="选择年份">
          <el-option label="全部" value="all" />
          <el-option
            v-for="year in yearOptions"
            :key="year"
            :label="year"
            :value="year">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="月份" prop="monthValue">
        <el-select v-model="queryParams.monthValue" placeholder="选择月份">
          <el-option label="全部" value="all" />
          <el-option
            v-for="dict in dict.type.month"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="type">
        <el-select v-model="queryParams.type" placeholder="选择类型">
          <el-option label="全部" value="all" />
          <el-option label="表扬" value="1" />
          <el-option label="待改进" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item label="组别" prop="groupId">
        <el-select v-model="queryParams.groupId" placeholder="选择组别">
          <el-option label="全部" value="all" />
          <el-option
            v-for="dept in deptOptions"
            :key="dept.deptId"
            :label="dept.deptName"
            :value="dept.deptId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="姓名" prop="employeeName">
        <el-input v-model="queryParams.employeeName" placeholder="请输入姓名" clearable />
      </el-form-item>
      <el-form-item label="岗位" prop="employeePositionType">
        <el-select v-model="queryParams.employeePositionType" placeholder="选择岗位">
          <el-option label="全部" value="all" />
          <el-option
            v-for="dict in dict.type.departments_cooperation_post_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="审批结果" prop="approvalResult">
        <el-select v-model="queryParams.approvalResult" placeholder="选择审批结果">
          <el-option label="全部" value="all" />
          <el-option label="同意" value="agree" />
          <el-option label="拒绝" value="refuse" />
        </el-select>
      </el-form-item>
      <el-form-item label="提交时间" prop="submitTime">
        <el-date-picker
          v-model="dateRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="handleDateRangeChange">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-data-line"
          size="mini"
          @click="goToStatistics"
        >部门合作度数据统计</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="interviewList">
      <el-table-column label="审批编号" align="center" prop="approvalId" width="160"/>
      <el-table-column label="年份" align="center" prop="yearValue" />
      <el-table-column label="月份" align="center" prop="monthValue">
        <template slot-scope="scope">
          {{ scope.row.monthValue }}月
        </template>
      </el-table-column>
      <el-table-column label="类型" align="center" prop="type">
        <template slot-scope="scope">
          <el-tag :type="scope.row.type === '1' ? 'success' : 'warning'">
            {{ scope.row.type === '1' ? '表扬' : '待改进' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="组别" align="center" prop="groupName" />
      <el-table-column label="姓名" align="center" prop="employeeName" />
      <el-table-column label="岗位" align="center" prop="employeePosition" />
      <el-table-column label="事项/问题说明" align="center" width="300">
        <template slot-scope="scope">
          <el-tooltip effect="dark" placement="top" popper-class="custom-tooltip">
            <div slot="content" style="max-width: 400px; white-space: normal; word-wrap: break-word; line-height: 1.5;">
              {{ scope.row.description }}
            </div>
            <div style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
              {{ scope.row.description }}
            </div>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="提交人" align="center" prop="submitterName" />
      <el-table-column label="提交人所属组" align="center" prop="submitterGroup" />
      <el-table-column label="审批结果" align="center" prop="approvalResult">
        <template slot-scope="scope">
          <el-tag :type="scope.row.approvalResult === 'agree' ? 'success' : 'danger'">
            {{ scope.row.approvalResult === 'agree' ? '同意' : '拒绝' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="提交时间" align="center" prop="submitTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.submitTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>

    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import {
  getDeptCooperationInterviewList
} from "@/api/process/monthlyDepartmentCooperationInterview";
import { deptSelect } from "@/api/commonBiz";

export default {
  name: "MonthlyDepartmentCooperationInterview",
  dicts: ['month', 'departments_cooperation_post_type'],
  data() {
    return {
      // 遮罩层
      loading: true,

      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 月度部门合作访谈表格数据
      interviewList: [],

      // 年份选项
      yearOptions: [],
      // 部门选项
      deptOptions: [],
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        yearValue: 'all',
        monthValue: 'all',
        type: 'all',
        groupId: 'all',
        employeeName: null,
        employeePositionType: 'all',
        approvalResult: 'all',
        submitTimeStart: null,
        submitTimeEnd: null
      },

    };
  },
  created() {
    this.initYearOptions();
    this.getDeptList();
    this.getList();
  },
  methods: {
    /** 初始化年份选项 */
    initYearOptions() {
      const currentYear = new Date().getFullYear();
      for (let i = 0; i < 5; i++) {
        this.yearOptions.push(currentYear - i);
      }
    },
    /** 查询部门列表 */
    getDeptList() {
      deptSelect().then(response => {
        this.deptOptions = response.data;
      });
    },
    /** 查询月度部门合作访谈列表 */
    getList() {
      this.loading = true;
      // 过滤查询参数，值为"all"或"-1"时不传值
      const params = { ...this.queryParams };
      Object.keys(params).forEach(key => {
        if (params[key] === 'all' || params[key] === '-1') {
          delete params[key];
        }
      });
      getDeptCooperationInterviewList(params).then(response => {
        this.interviewList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 处理日期范围变化 */
    handleDateRangeChange(value) {
      if (value && value.length === 2) {
        this.queryParams.submitTimeStart = value[0];
        this.queryParams.submitTimeEnd = value[1];
      } else {
        this.queryParams.submitTimeStart = null;
        this.queryParams.submitTimeEnd = null;
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.queryParams.submitTimeStart = null;
      this.queryParams.submitTimeEnd = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 跳转到统计页面 */
    goToStatistics() {
      this.$router.push({path: '/process/deptCooperationStatistics'});
    }

  }
};
</script>

<style>
/* 自定义tooltip样式 */
.custom-tooltip {
  max-width: 400px !important;
}

.custom-tooltip .el-tooltip__popper {
  max-width: 400px !important;
  white-space: normal !important;
  word-wrap: break-word !important;
  line-height: 1.5 !important;
}
</style>
